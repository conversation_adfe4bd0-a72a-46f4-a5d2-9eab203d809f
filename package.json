{"name": "home-mcp", "version": "1.0.0", "description": "A Model Context Protocol server implementation", "type": "module", "bin": "dist/index.js", "scripts": {"build": "tsc", "dev": "tsc --watch", "start": "node dist/index.js", "clean": "rm -rf dist", "test": "yarn build && yarn test:inspector", "test:inspector": "npx @modelcontextprotocol/inspector dist/index.js"}, "engines": {"node": ">=22.14.0"}, "packageManager": "yarn@4.9.1", "keywords": ["mcp", "model-context-protocol", "ai", "llm"], "author": "<PERSON>", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^1.12.1", "zod": "^3.25.45"}, "devDependencies": {"@modelcontextprotocol/inspector": "^0.13.0", "@tsconfig/node22": "^22.0.2", "@tsconfig/strictest": "^2.0.5", "@types/node": "^22.15.29", "typescript": "^5.8.3"}}